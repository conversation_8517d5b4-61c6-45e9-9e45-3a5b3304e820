import React from 'react';
import ProjectCard from './ProjectCard';
import { useProjects } from '../hooks/useProjects';

const OngoingProject = ({ inView, onProjectClick }) => {
  // Use the centralized hook to prevent duplicate API calls
  const { projects: ongoingProjects, isLoading, error, refetch } = useProjects('ongoing', {
    limit: 6,
    autoFetch: true
  });

  if (error) {
    return (
      <div className="error-state">
        <h3>Error Loading Projects</h3>
        <p>{error}</p>
        <button onClick={refetch} className="btn btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="loading-state">
        <div className="loading-spinner"></div>
        <p>Loading ongoing projects...</p>
      </div>
    );
  }

  if (ongoingProjects.length === 0) {
    return (
      <div className="empty-state">
        <h3>No ongoing projects</h3>
        <p>All projects are currently in planning or completed phase.</p>
      </div>
    );
  }

  return (
    <div className="projects-grid">
      {ongoingProjects.map((project, index) => (
        <ProjectCard
          key={project.id}
          project={project}
          index={index}
          inView={inView}
          onClick={() => onProjectClick(project)}
        />
      ))}
    </div>
  );
};

export default OngoingProject;