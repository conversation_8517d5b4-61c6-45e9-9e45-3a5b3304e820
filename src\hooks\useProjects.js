import { useEffect, useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchProjects,
  selectProjectsByStatus,
  selectProjectsLoadingByStatus,
  selectProjectsError
} from '../store/slices/projectsSlice';

// Fallback to local data if API is not available
const useFallbackData = () => {
  const [shouldUseFallback, setShouldUseFallback] = useState(false);

  useEffect(() => {
    // Check if API is available
    const checkAPI = async () => {
      try {
        const response = await fetch('/api/projects?limit=1');
        if (!response.ok) {
          setShouldUseFallback(true);
        }
      } catch (error) {
        console.warn('API not available, using fallback data');
        setShouldUseFallback(true);
      }
    };

    checkAPI();
  }, []);

  return shouldUseFallback;
};

// Centralized hook to manage project data fetching
export const useProjects = (status = 'all', options = {}) => {
  const dispatch = useDispatch();
  const {
    limit = 12,
    page = 1,
    autoFetch = true,
    refetchOnMount = false
  } = options;

  // Get projects and loading state for the specific status
  const projects = useSelector(selectProjectsByStatus(status));
  const isLoading = useSelector(selectProjectsLoadingByStatus(status));
  const error = useSelector(selectProjectsError);

  // Memoized fetch function
  const fetchProjectsData = useCallback((params = {}) => {
    const fetchParams = {
      status: status === 'all' ? undefined : status,
      limit,
      page,
      ...params
    };

    return dispatch(fetchProjects(fetchParams));
  }, [dispatch, status, limit, page]);

  // Auto-fetch on mount if enabled and no data exists
  useEffect(() => {
    if (autoFetch) {
      const shouldFetch = refetchOnMount || projects.length === 0;
      
      if (shouldFetch && !isLoading) {
        fetchProjectsData();
      }
    }
  }, [autoFetch, refetchOnMount, projects.length, isLoading, fetchProjectsData]);

  return {
    projects,
    isLoading,
    error,
    refetch: fetchProjectsData,
    hasData: projects.length > 0
  };
};

// Hook for multiple project statuses (used in Projects page)
export const useMultipleProjectStatuses = (statuses = ['ongoing', 'upcoming', 'completed']) => {
  const dispatch = useDispatch();
  
  const projectsData = {};
  const loadingStates = {};
  let hasAnyLoading = false;
  let hasAnyError = false;

  // Get data for each status
  statuses.forEach(status => {
    projectsData[status] = useSelector(selectProjectsByStatus(status));
    loadingStates[status] = useSelector(selectProjectsLoadingByStatus(status));
    
    if (loadingStates[status]) hasAnyLoading = true;
  });

  const error = useSelector(selectProjectsError);
  if (error) hasAnyError = true;

  // Fetch all statuses that don't have data
  const fetchAllStatuses = useCallback(() => {
    const promises = [];
    
    statuses.forEach(status => {
      if (projectsData[status].length === 0 && !loadingStates[status]) {
        promises.push(
          dispatch(fetchProjects({
            status,
            limit: 12,
            page: 1
          }))
        );
      }
    });

    return Promise.all(promises);
  }, [dispatch, statuses, projectsData, loadingStates]);

  // Auto-fetch missing data
  useEffect(() => {
    const hasEmptyStatus = statuses.some(status => 
      projectsData[status].length === 0 && !loadingStates[status]
    );

    if (hasEmptyStatus && !hasAnyLoading) {
      fetchAllStatuses();
    }
  }, [statuses, projectsData, loadingStates, hasAnyLoading, fetchAllStatuses]);

  // Combine all projects for 'all' view
  const allProjects = statuses.reduce((acc, status) => {
    return [...acc, ...projectsData[status]];
  }, []);

  return {
    projectsData,
    loadingStates,
    allProjects,
    isLoading: hasAnyLoading,
    error: hasAnyError ? error : null,
    refetchAll: fetchAllStatuses,
    hasData: allProjects.length > 0
  };
};

// Hook for featured projects
export const useFeaturedProjects = (limit = 6) => {
  const dispatch = useDispatch();
  
  const allProjects = useSelector(selectProjectsByStatus('all'));
  const isLoading = useSelector(selectProjectsLoadingByStatus('all'));
  const error = useSelector(selectProjectsError);

  // Filter featured projects
  const featuredProjects = allProjects
    .filter(project => project.featured)
    .slice(0, limit);

  // Fetch if no data
  useEffect(() => {
    if (allProjects.length === 0 && !isLoading) {
      dispatch(fetchProjects({ featured: true, limit }));
    }
  }, [dispatch, allProjects.length, isLoading, limit]);

  return {
    projects: featuredProjects,
    isLoading,
    error,
    hasData: featuredProjects.length > 0
  };
};

// Hook for project statistics
export const useProjectStats = () => {
  const { projectsData, isLoading } = useMultipleProjectStatuses();
  
  const stats = {
    total: 0,
    ongoing: 0,
    upcoming: 0,
    completed: 0,
    featured: 0
  };

  Object.entries(projectsData).forEach(([status, projects]) => {
    stats[status] = projects.length;
    stats.total += projects.length;
    stats.featured += projects.filter(p => p.featured).length;
  });

  return {
    stats,
    isLoading
  };
};

export default useProjects;
