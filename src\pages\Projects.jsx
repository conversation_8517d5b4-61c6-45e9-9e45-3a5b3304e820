import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useDispatch, useSelector } from 'react-redux';
import { setFilters } from '../store/slices/projectsSlice';
import { selectIsAdmin } from '../store/slices/authSlice';
import { useMultipleProjectStatuses } from '../hooks/useProjects';
import ProjectCard from '../components/ProjectCard';
import '../styles/Projects.css';

const Projects = () => {
  const dispatch = useDispatch();
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  // Use the centralized hook to prevent multiple API calls
  const {
    projectsData,
    loadingStates,
    allProjects,
    isLoading,
    error,
    refetchAll
  } = useMultipleProjectStatuses(['ongoing', 'upcoming', 'completed']);

  const isAdmin = useSelector(selectIsAdmin);
  const [selectedProject, setSelectedProject] = useState(null);
  const [activeTab, setActiveTab] = useState('all');

  // Get the appropriate projects and loading state based on active tab
  const getCurrentProjects = () => {
    switch (activeTab) {
      case 'ongoing':
        return projectsData.ongoing || [];
      case 'upcoming':
        return projectsData.upcoming || [];
      case 'completed':
        return projectsData.completed || [];
      case 'all':
      default:
        return allProjects;
    }
  };

  const getCurrentLoading = () => {
    switch (activeTab) {
      case 'ongoing':
        return loadingStates.ongoing;
      case 'upcoming':
        return loadingStates.upcoming;
      case 'completed':
        return loadingStates.completed;
      case 'all':
      default:
        return isLoading;
    }
  };

  const filteredProjects = getCurrentProjects();
  const currentLoading = getCurrentLoading();

  // The useMultipleProjectStatuses hook handles all the fetching automatically
  // No need for manual useEffect here

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    const statusFilter = tab === 'all' ? '' : tab;
    dispatch(setFilters({ status: statusFilter }));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: -30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  if (error) {
    return (
      <section className="projects-page">
        <div className="container">
          <div className="error-state">
            <h2>Error Loading Projects</h2>
            <p>{error}</p>
            <button
              onClick={refetchAll}
              className="btn btn-primary"
            >
              Try Again
            </button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="projects-page" ref={ref}>
      <div className="container">
        <motion.div
          className="page-header"
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={titleVariants}
        >
          <h1>Our Projects</h1>
          <p>Discover our premium real estate developments across prime locations</p>
          {isAdmin && (
            <div className="admin-actions">
              <button className="btn btn-primary">
                + Add New Project
              </button>
            </div>
          )}

          <div className="project-tabs">
            <button
              className={`tab-btn ${activeTab === 'all' ? 'active' : ''}`}
              onClick={() => handleTabChange('all')}
            >
              All Projects ({allProjects.length})
            </button>
            <button
              className={`tab-btn ${activeTab === 'ongoing' ? 'active' : ''}`}
              onClick={() => handleTabChange('ongoing')}
            >
              Ongoing ({projectsData.ongoing?.length || 0})
            </button>
            <button
              className={`tab-btn ${activeTab === 'upcoming' ? 'active' : ''}`}
              onClick={() => handleTabChange('upcoming')}
            >
              Upcoming ({projectsData.upcoming?.length || 0})
            </button>
            <button
              className={`tab-btn ${activeTab === 'completed' ? 'active' : ''}`}
              onClick={() => handleTabChange('completed')}
            >
              Completed ({projectsData.completed?.length || 0})
            </button>
          </div>
        </motion.div>

        {currentLoading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading {activeTab === 'all' ? 'all' : activeTab} projects...</p>
          </div>
        ) : (
          <motion.div
            className="projects-grid"
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
          >
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  index={index}
                  inView={inView}
                  onClick={() => setSelectedProject(project)}
                  isAdmin={isAdmin}
                />
              ))
            ) : (
              <div className="empty-state">
                <h3>No {activeTab === 'all' ? '' : activeTab} projects found</h3>
                <p>No projects match the current filter criteria.</p>
                {isAdmin && (
                  <button className="btn btn-primary">
                    + Add First Project
                  </button>
                )}
              </div>
            )}
          </motion.div>
        )}

        {/* Pagination could be added here if needed */}
        {filteredProjects.length > 0 && (
          <div className="pagination">
            <p>
              Showing {filteredProjects.length} projects
            </p>
            {/* Add pagination controls here if needed */}
          </div>
        )}
      </div>

      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="project-modal"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              className="modal-content"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button 
                className="modal-close"
                onClick={() => setSelectedProject(null)}
              >
                ×
              </button>
              <div className="modal-gallery">
                {selectedProject.gallery && selectedProject.gallery.map((image, idx) => (
                  <img key={idx} src={image} alt={`${selectedProject.title} ${idx + 1}`} />
                ))}
              </div>
              <div className="modal-info">
                <h3>{selectedProject.title}</h3>
                <p className="modal-location">{selectedProject.location}</p>
                <p className="modal-description">{selectedProject.description}</p>
                <div className="modal-features">
                  {selectedProject.features && selectedProject.features.map((feature, idx) => (
                    <span key={idx} className="modal-feature">
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default Projects;