import React from 'react';
import ProjectCard from './ProjectCard';
import { useProjects } from '../hooks/useProjects';

const UpcomingProjects = ({ inView, onProjectClick }) => {
  // Use the centralized hook to prevent duplicate API calls
  const { projects: upcomingProjects, isLoading, error, refetch } = useProjects('upcoming', {
    limit: 6,
    autoFetch: true
  });
  
  if (error) {
    return (
      <div className="error-state">
        <h3>Error Loading Projects</h3>
        <p>{error}</p>
        <button onClick={refetch} className="btn btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="loading-state">
        <div className="loading-spinner"></div>
        <p>Loading upcoming projects...</p>
      </div>
    );
  }

  if (upcomingProjects.length === 0) {
    return (
      <div className="empty-state">
        <h3>No upcoming projects</h3>
        <p>Check back soon for exciting new developments!</p>
      </div>
    );
  }

  return (
    <div className="projects-grid">
      {upcomingProjects.map((project, index) => (
        <ProjectCard
          key={project.id}
          project={project}
          index={index}
          inView={inView}
          onClick={() => onProjectClick(project)}
        />
      ))}
    </div>
  );
};

export default UpcomingProjects;