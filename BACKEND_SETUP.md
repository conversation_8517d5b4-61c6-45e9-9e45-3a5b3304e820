# Backend Server Setup Guide

## CORS Configuration Required

Your backend server needs to be configured to handle CORS requests from the frontend. Here's what you need to add:

### Express.js CORS Configuration

```javascript
const cors = require('cors');

// CORS configuration
const corsOptions = {
  origin: [
    'http://localhost:5173', // Vite dev server
    'http://localhost:3000', // Alternative dev port
    'https://your-frontend-domain.com' // Production domain
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'Accept',
    'Origin',
    'X-Requested-With'
  ]
};

app.use(cors(corsOptions));

// Handle preflight requests
app.options('*', cors(corsOptions));
```

### Required API Endpoints

Your backend should implement these endpoints:

#### Projects API
- `GET /api/projects` - Get all projects with optional filters
  - Query params: `status`, `limit`, `page`, `featured`, `type`, `location`
- `GET /api/projects/:id` - Get single project
- `POST /api/projects` - Create project (admin only)
- `PUT /api/projects/:id` - Update project (admin only)
- `DELETE /api/projects/:id` - Delete project (admin only)
- `PATCH /api/projects/:id/featured` - Toggle featured status

#### Upload API
- `POST /api/upload/project-images` - Upload project images
- `POST /api/upload/site-images` - Upload site images
- `POST /api/upload/floor-plans` - Upload floor plans

#### Response Format

All API responses should follow this format:

```javascript
// Success Response
{
  "success": true,
  "data": {
    "projects": [...],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 50,
      "pages": 5
    }
  },
  "message": "Projects fetched successfully"
}

// Error Response
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE"
  }
}
```

### Image Handling

#### File Upload Configuration
- Store uploaded images in `/uploads/` directory
- Support formats: JPEG, PNG, WebP
- Generate thumbnails for better performance
- Return full URLs in API responses

#### Image URL Format
```javascript
// Backend should return full URLs
{
  "images": [
    "http://localhost:5000/uploads/projects/image1.jpg",
    "http://localhost:5000/uploads/projects/image2.jpg"
  ],
  "gallery": [
    "http://localhost:5000/uploads/gallery/gallery1.jpg"
  ]
}
```

### Database Schema

#### Project Schema Example
```javascript
{
  _id: ObjectId,
  title: String,
  location: String,
  status: String, // 'ongoing', 'upcoming', 'completed', 'under-construction'
  progress: Number, // 0-100 for ongoing projects
  images: [String], // Array of image URLs
  gallery: [String], // Array of gallery image URLs
  brochure: String, // Brochure file URL
  price: {
    min: Number,
    max: Number,
    currency: String
  },
  description: String,
  amenities: [String],
  type: String, // 'residential', 'commercial', 'mixed'
  developer: String,
  totalUnits: Number,
  availableUnits: Number,
  specifications: Object,
  floorPlans: [String],
  virtualTour: String,
  nearbyFacilities: [String],
  legalApprovals: [String],
  paymentPlans: [Object],
  contactInfo: Object,
  featured: Boolean,
  timeline: {
    startDate: Date,
    expectedCompletion: Date,
    phases: [Object]
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Environment Variables

Create a `.env` file in your backend:

```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/realstate
JWT_SECRET=your-jwt-secret
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/webp
```

### Static File Serving

Configure your backend to serve uploaded files:

```javascript
// Serve static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
```

### Testing the API

You can test your API endpoints using:

1. **Postman** - Import the collection
2. **curl** commands:
```bash
# Test projects endpoint
curl -X GET http://localhost:5000/api/projects

# Test with filters
curl -X GET "http://localhost:5000/api/projects?status=ongoing&limit=6"
```

### Common Issues and Solutions

1. **CORS Errors**: Ensure CORS is properly configured
2. **Image URLs**: Return full URLs, not relative paths
3. **File Uploads**: Configure multer properly for file handling
4. **Authentication**: Implement JWT token validation
5. **Error Handling**: Return consistent error responses

### Development vs Production

- **Development**: Use `http://localhost:5000`
- **Production**: Use your actual domain with HTTPS
- Update CORS origins accordingly
- Use environment variables for configuration
